<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        Quick Actions
      </h3>
    </div>

    <!-- Actions Grid -->
    <div class="p-6">
      <div class="grid grid-cols-2 gap-4">
        <!-- Create Auction -->
        <router-link
          to="/admin-spa/auctions/create"
          class="group flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        >
          <div class="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-lg mb-3 group-hover:bg-blue-200 dark:group-hover:bg-blue-700 transition-colors">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-center">
            Create Auction
          </span>
        </router-link>

        <!-- Add Item -->
        <router-link
          to="/admin-spa/items/create"
          class="group flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
        >
          <div class="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-800 rounded-lg mb-3 group-hover:bg-green-200 dark:group-hover:bg-green-700 transition-colors">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-center">
            Add Item
          </span>
        </router-link>

        <!-- Add User -->
        <router-link
          to="/admin-spa/users/create"
          class="group flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
        >
          <div class="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-lg mb-3 group-hover:bg-purple-200 dark:group-hover:bg-purple-700 transition-colors">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-center">
            Add User
          </span>
        </router-link>

        <!-- View Reports -->
        <router-link
          to="/admin-spa/reports"
          class="group flex flex-col items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
        >
          <div class="flex items-center justify-center w-12 h-12 bg-yellow-100 dark:bg-yellow-800 rounded-lg mb-3 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-700 transition-colors">
            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-gray-100 text-center">
            View Reports
          </span>
        </router-link>
      </div>

      <!-- Additional Actions -->
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          Management
        </h4>
        
        <div class="grid grid-cols-1 gap-3">
          <!-- Bulk Import -->
          <router-link
            to="/admin-spa/items/bulk-import"
            class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
          >
            <div class="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-lg mr-3 group-hover:bg-gray-300 dark:group-hover:bg-gray-500 transition-colors">
              <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Bulk Import Items
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Import multiple items from CSV/Excel
              </p>
            </div>
          </router-link>

          <!-- System Settings -->
          <router-link
            to="/admin-spa/settings"
            class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
          >
            <div class="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-lg mr-3 group-hover:bg-gray-300 dark:group-hover:bg-gray-500 transition-colors">
              <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                System Settings
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Configure system preferences
              </p>
            </div>
          </router-link>

          <!-- User Roles -->
          <router-link
            to="/admin-spa/users/roles"
            class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group"
          >
            <div class="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-lg mr-3 group-hover:bg-gray-300 dark:group-hover:bg-gray-500 transition-colors">
              <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Manage Roles
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Configure user roles and permissions
              </p>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// This component is purely presentational and uses router-link for navigation
// No additional logic needed for now
</script>

<style scoped>
/* Ensure consistent hover states */
.group:hover .group-hover\:bg-blue-200 {
  background-color: rgb(191 219 254);
}

.group:hover .group-hover\:bg-green-200 {
  background-color: rgb(187 247 208);
}

.group:hover .group-hover\:bg-purple-200 {
  background-color: rgb(221 214 254);
}

.group:hover .group-hover\:bg-yellow-200 {
  background-color: rgb(254 240 138);
}

.group:hover .group-hover\:bg-gray-300 {
  background-color: rgb(209 213 219);
}

/* Dark mode hover states */
.dark .group:hover .group-hover\:bg-blue-700 {
  background-color: rgb(29 78 216);
}

.dark .group:hover .group-hover\:bg-green-700 {
  background-color: rgb(21 128 61);
}

.dark .group:hover .group-hover\:bg-purple-700 {
  background-color: rgb(109 40 217);
}

.dark .group:hover .group-hover\:bg-yellow-700 {
  background-color: rgb(161 98 7);
}

.dark .group:hover .group-hover\:bg-gray-500 {
  background-color: rgb(107 114 128);
}
</style>
