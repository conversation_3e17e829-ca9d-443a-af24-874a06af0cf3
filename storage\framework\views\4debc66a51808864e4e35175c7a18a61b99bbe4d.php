<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Vertigo AMS')); ?> - Admin Panel</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css']); ?>

    <!-- Meta tags for PWA -->
    <meta name="theme-color" content="#0068ff">
    <meta name="description" content="Vertigo AMS Admin Panel - Manage your auction platform">

    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo e(asset('images/logo.png')); ?>" as="image" type="image/png">
</head>
<body class="bg-gray-100 antialiased">
    <!-- Admin App Container -->
    <div id="admin-app">
        <!-- Loading State -->
        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
            <div class="text-center">
                <!-- Logo -->
                <div class="w-16 h-16 mx-auto mb-4">
                    <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                        <circle fill="#243b53" cx="130" cy="130" r="130"/>
                        <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                        <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                        <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                        <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                        <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                        <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                    </svg>
                </div>
                
                <!-- Loading Text -->
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Vertigo AMS</h1>
                <p class="text-gray-600 mb-4">Admin Panel</p>
                
                <!-- Loading Spinner -->
                <div class="inline-flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
                    <span class="text-sm text-gray-500">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Pass server data to JavaScript -->
    <script>
        window.user = <?php echo json_encode(auth()->user(), 15, 512) ?>;
        window.csrfToken = '<?php echo e(csrf_token()); ?>';
        window.appUrl = '<?php echo e(config('app.url')); ?>';
        window.appName = '<?php echo e(config('app.name')); ?>';
        window.branches = <?php echo json_encode(\App\Models\Branch::whereNull('deleted_at')->get(), 15, 512) ?>;

        // Admin-specific configuration
        window.adminConfig = {
            version: '1.0.0',
            environment: '<?php echo e(app()->environment()); ?>',
            debug: <?php echo e(config('app.debug') ? 'true' : 'false'); ?>,
            timezone: '<?php echo e(config('app.timezone')); ?>',
            locale: '<?php echo e(app()->getLocale()); ?>'
        };

        // Performance monitoring
        window.performance?.mark?.('admin-app-start');
    </script>

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app-admin.ts']); ?>

    <!-- Performance monitoring -->
    <script>
        window.addEventListener('load', function() {
            window.performance?.mark?.('admin-app-loaded');
            
            // Measure loading time
            if (window.performance?.measure) {
                try {
                    window.performance.measure('admin-app-load-time', 'admin-app-start', 'admin-app-loaded');
                    const measure = window.performance.getEntriesByName('admin-app-load-time')[0];
                    console.log(`Admin app loaded in ${Math.round(measure.duration)}ms`);
                } catch (e) {
                    // Ignore measurement errors
                }
            }
        });
    </script>

    <!-- Error handling -->
    <script>
        window.addEventListener('error', function(event) {
            console.error('Admin app error:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Admin app unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/admin/spa-app.blade.php ENDPATH**/ ?>