<template>
  <AdminPageTemplate
    :title="title"
    :subtitle="subtitle"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
  >
    <template #actions>
      <slot name="actions">
        <Button
          v-if="showCreateButton"
          variant="primary"
          size="sm"
          @click="handleCreate"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ createButtonText }}
        </Button>
      </slot>
    </template>

    <!-- Filters and Search -->
    <Card v-if="showFilters" class="p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
        <!-- Search -->
        <div class="flex-1 max-w-md">
          <Input
            v-model="searchQuery"
            type="search"
            placeholder="Search..."
            @input="handleSearch"
          >
            <template #prefix>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </template>
          </Input>
        </div>

        <!-- Filters -->
        <div v-if="$slots.filters" class="flex items-center space-x-3">
          <slot name="filters" />
        </div>

        <!-- Actions -->
        <div v-if="$slots.listActions" class="flex items-center space-x-3">
          <slot name="listActions" />
        </div>
      </div>
    </Card>

    <!-- Data Table -->
    <Card class="overflow-hidden">
      <div v-if="showBulkActions && selectedItems.length > 0" class="bg-blue-50 border-b border-blue-200 px-6 py-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-blue-700">
            {{ selectedItems.length }} item{{ selectedItems.length === 1 ? '' : 's' }} selected
          </span>
          <div v-if="$slots.bulkActions" class="flex items-center space-x-2">
            <slot name="bulkActions" :selectedItems="selectedItems" />
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th v-if="showBulkActions" class="w-12 px-6 py-3">
                <input
                  type="checkbox"
                  :checked="allSelected"
                  :indeterminate="someSelected"
                  @change="toggleSelectAll"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th
                v-for="column in columns"
                :key="column.key"
                :class="[
                  'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                  column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                ]"
                @click="column.sortable ? handleSort(column.key) : null"
              >
                <div class="flex items-center space-x-1">
                  <span>{{ column.label }}</span>
                  <svg
                    v-if="column.sortable && sortBy === column.key"
                    :class="[
                      'w-4 h-4',
                      sortOrder === 'asc' ? 'transform rotate-180' : ''
                    ]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </th>
              <th v-if="showActions" class="w-24 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <slot name="rows" :items="items" :selectedItems="selectedItems" />
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="items.length === 0 && !loading" class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0V3" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ emptyStateTitle }}</h3>
        <p class="text-gray-500 mb-4">{{ emptyStateMessage }}</p>
        <Button v-if="showCreateButton" variant="primary" @click="handleCreate">
          {{ createButtonText }}
        </Button>
      </div>
    </Card>

    <!-- Pagination -->
    <div v-if="showPagination && totalPages > 1" class="flex justify-center">
      <Pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="totalItems"
        :per-page="perPage"
        @page-change="handlePageChange"
      />
    </div>
  </AdminPageTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import AdminPageTemplate from './AdminPageTemplate.vue';
import { Button, Card, Input, Pagination } from '@/components/ui';

// Debug logging
onMounted(() => {
  console.log('AdminListTemplate mounted - Vue 3 Composition API working!');
});

interface Column {
  key: string;
  label: string;
  sortable?: boolean;
}

interface Props {
  title?: string;
  subtitle?: string;
  loading?: boolean;
  error?: string | null;
  breadcrumbs?: any[];
  items?: any[];
  columns?: Column[];
  selectedItems?: any[];
  showFilters?: boolean;
  showBulkActions?: boolean;
  showActions?: boolean;
  showCreateButton?: boolean;
  createButtonText?: string;
  createRoute?: string;
  emptyStateTitle?: string;
  emptyStateMessage?: string;
  showPagination?: boolean;
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  perPage?: number;
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  columns: () => [],
  selectedItems: () => [],
  showFilters: true,
  showBulkActions: false,
  showActions: true,
  showCreateButton: true,
  createButtonText: 'Create New',
  emptyStateTitle: 'No items found',
  emptyStateMessage: 'Get started by creating your first item.',
  showPagination: true,
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  perPage: 20
});

const emit = defineEmits<{
  create: [];
  search: [query: string];
  sort: [column: string, order: 'asc' | 'desc'];
  pageChange: [page: number];
  selectAll: [selected: boolean];
  selectItem: [item: any, selected: boolean];
}>();

// Local state
const searchQuery = ref('');
const sortBy = ref('');
const sortOrder = ref<'asc' | 'desc'>('asc');

// Computed
const allSelected = computed(() => {
  return props.items.length > 0 && props.selectedItems.length === props.items.length;
});

const someSelected = computed(() => {
  return props.selectedItems.length > 0 && props.selectedItems.length < props.items.length;
});

// Methods
const handleCreate = () => {
  emit('create');
};

const handleSearch = () => {
  emit('search', searchQuery.value);
};

const handleSort = (column: string) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = column;
    sortOrder.value = 'asc';
  }
  emit('sort', column, sortOrder.value);
};

const handlePageChange = (page: number) => {
  emit('pageChange', page);
};

const toggleSelectAll = () => {
  emit('selectAll', !allSelected.value);
};
</script>
