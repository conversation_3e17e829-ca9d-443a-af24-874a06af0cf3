<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ItemController;
use App\Http\Controllers\ItemController as PublicItemController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\AdvertController;
use App\Http\Controllers\Api\StatusController;
use App\Http\Controllers\Api\AccountController;
use App\Http\Controllers\Api\AuctionController;
use App\Http\Controllers\Api\WatchlistController;
use App\Http\Controllers\Api\BidDashboardController;
use App\Http\Controllers\Api\UserUsersController;
use App\Http\Controllers\Api\UserItemsController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\AuctionTypeController;
use App\Http\Controllers\Api\UserAdvertsController;
use App\Http\Controllers\Api\TransactionController;
use App\Http\Controllers\Api\UserStatusesController;
use App\Http\Controllers\Api\UserAuctionsController;
use App\Http\Controllers\Api\UserAccountsController;
use App\Http\Controllers\Api\AuctionTypeItemsController;
use App\Http\Controllers\Api\UserAuctionTypesController;
use App\Http\Controllers\Api\UserTransactionsController;
use App\Http\Controllers\Api\StatusAuctionTypesController;
use App\Http\Controllers\Api\AccountTransactionsController;
use App\Http\Controllers\Api\AuctionTypeAuctionsController;
use App\Http\Controllers\Api\AdminDashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('/login', [AuthController::class, 'login'])->name('api.login');
Route::post('/register', [AuthController::class, 'register'])->name('api.register');

// Public routes for homepage
Route::get('/adverts', [AdvertController::class, 'index'])->name('api.public.adverts');



Route::middleware('auth:sanctum')
    ->get('/user', function (Request $request) {
        return $request->user();
    })
    ->name('api.user');

Route::name('api.')
    ->middleware('auth:sanctum')
    ->group(function () {
        Route::apiResource('roles', RoleController::class);
        Route::apiResource('permissions', PermissionController::class);

        Route::apiResource('accounts', AccountController::class);

        // Account Transations
        Route::get('/accounts/{account}/transactions', [
            AccountTransactionsController::class,
            'index',
        ])->name('accounts.transactions.index');
        Route::post('/accounts/{account}/transactions', [
            AccountTransactionsController::class,
            'store',
        ])->name('accounts.transactions.store');

        Route::apiResource('adverts', AdvertController::class);

        Route::apiResource('auction-types', AuctionTypeController::class);

        // AuctionType Items
        Route::get('/auction-types/{auctionType}/items', [
            AuctionTypeItemsController::class,
            'index',
        ])->name('auction-types.items.index');
        Route::post('/auction-types/{auctionType}/items', [
            AuctionTypeItemsController::class,
            'store',
        ])->name('auction-types.items.store');

        // AuctionType Auctions
        Route::get('/auction-types/{auctionType}/auctions', [
            AuctionTypeAuctionsController::class,
            'index',
        ])->name('auction-types.auctions.index');
        Route::post('/auction-types/{auctionType}/auctions', [
            AuctionTypeAuctionsController::class,
            'store',
        ])->name('auction-types.auctions.store');

        Route::apiResource('users', UserController::class);

        // User Users
        Route::get('/users/{user}/users', [
            UserUsersController::class,
            'index',
        ])->name('users.users.index');
        Route::post('/users/{user}/users', [
            UserUsersController::class,
            'store',
        ])->name('users.users.store');

        // User Users2
        Route::get('/users/{user}/users', [
            UserUsersController::class,
            'index',
        ])->name('users.users.index');
        Route::post('/users/{user}/users', [
            UserUsersController::class,
            'store',
        ])->name('users.users.store');

        // User Statuses2
        Route::get('/users/{user}/statuses', [
            UserStatusesController::class,
            'index',
        ])->name('users.statuses.index');
        Route::post('/users/{user}/statuses', [
            UserStatusesController::class,
            'store',
        ])->name('users.statuses.store');

        // User Auction Types
        Route::get('/users/{user}/auction-types', [
            UserAuctionTypesController::class,
            'index',
        ])->name('users.auction-types.index');
        Route::post('/users/{user}/auction-types', [
            UserAuctionTypesController::class,
            'store',
        ])->name('users.auction-types.store');

        // User Auction Types2
        Route::get('/users/{user}/auction-types', [
            UserAuctionTypesController::class,
            'index',
        ])->name('users.auction-types.index');
        Route::post('/users/{user}/auction-types', [
            UserAuctionTypesController::class,
            'store',
        ])->name('users.auction-types.store');

        // User Auctions
        Route::get('/users/{user}/auctions', [
            UserAuctionsController::class,
            'index',
        ])->name('users.auctions.index');
        Route::post('/users/{user}/auctions', [
            UserAuctionsController::class,
            'store',
        ])->name('users.auctions.store');

        // User Auctions2
        Route::get('/users/{user}/auctions', [
            UserAuctionsController::class,
            'index',
        ])->name('users.auctions.index');
        Route::post('/users/{user}/auctions', [
            UserAuctionsController::class,
            'store',
        ])->name('users.auctions.store');

        // User Accounts
        Route::get('/users/{user}/accounts', [
            UserAccountsController::class,
            'index',
        ])->name('users.accounts.index');
        Route::post('/users/{user}/accounts', [
            UserAccountsController::class,
            'store',
        ])->name('users.accounts.store');

        // User Accounts2
        Route::get('/users/{user}/accounts', [
            UserAccountsController::class,
            'index',
        ])->name('users.accounts.index');
        Route::post('/users/{user}/accounts', [
            UserAccountsController::class,
            'store',
        ])->name('users.accounts.store');

        // User Items
        Route::get('/users/{user}/items', [
            UserItemsController::class,
            'index',
        ])->name('users.items.index');
        Route::post('/users/{user}/items', [
            UserItemsController::class,
            'store',
        ])->name('users.items.store');

        // User Items2
        Route::get('/users/{user}/items', [
            UserItemsController::class,
            'index',
        ])->name('users.items.index');
        Route::post('/users/{user}/items', [
            UserItemsController::class,
            'store',
        ])->name('users.items.store');

        // User Transations
        Route::get('/users/{user}/transactions', [
            UserTransactionsController::class,
            'index',
        ])->name('users.transactions.index');
        Route::post('/users/{user}/transactions', [
            UserTransactionsController::class,
            'store',
        ])->name('users.transactions.store');

        // User Transations
        Route::get('/users/{user}/transactions', [
            UserTransactionsController::class,
            'index',
        ])->name('users.transactions.index');
        Route::post('/users/{user}/transactions', [
            UserTransactionsController::class,
            'store',
        ])->name('users.transactions.store');

        // User Adverts
        Route::get('/users/{user}/adverts', [
            UserAdvertsController::class,
            'index',
        ])->name('users.adverts.index');
        Route::post('/users/{user}/adverts', [
            UserAdvertsController::class,
            'store',
        ])->name('users.adverts.store');

        // User Adverts2
        Route::get('/users/{user}/adverts', [
            UserAdvertsController::class,
            'index',
        ])->name('users.adverts.index');
        Route::post('/users/{user}/adverts', [
            UserAdvertsController::class,
            'store',
        ])->name('users.adverts.store');

        Route::apiResource('transactions', TransactionController::class);

        Route::apiResource('statuses', StatusController::class);

        // Status Auction Types
        Route::get('/statuses/{status}/auction-types', [
            StatusAuctionTypesController::class,
            'index',
        ])->name('statuses.auction-types.index');
        Route::post('/statuses/{status}/auction-types', [
            StatusAuctionTypesController::class,
            'store',
        ])->name('statuses.auction-types.store');

        // Status Auction Types2
        Route::get('/statuses/{status}/auction-types', [
            StatusAuctionTypesController::class,
            'index',
        ])->name('statuses.auction-types.index');
        Route::post('/statuses/{status}/auction-types', [
            StatusAuctionTypesController::class,
            'store',
        ])->name('statuses.auction-types.store');

        Route::apiResource('items', ItemController::class);

        // Item bids route
        Route::get('/items/{item}/bids', [ItemController::class, 'bids']);

        Route::apiResource('auctions', AuctionController::class);

        Route::get('live-auctions', [AuctionController::class, 'liveAuctions']);
        Route::get('online-auctions', [AuctionController::class, 'onlineAuctions']);
        Route::get('check-subscription/{auctionTypeId}', [AuctionController::class, 'checkSubscription']);

        Route::get('/check-deposit/{reference_number}', [ TransactionController::class, 'checkDeposit' ]);
        Route::post('/check-deposit/{reference_number}', [ TransactionController::class, 'checkDeposit' ]);
        Route::get('/place-a-bid/{item}/{amount}', [ AuctionController::class, 'placeABid' ]);
        Route::post('/place-a-bid/{item}/{amount}', [ AuctionController::class, 'placeABid' ]);
        Route::post('/update-auction/{code}', [ AuctionController::class, 'updateAuction' ]);

        // Watchlist routes
        Route::get('/watchlist', [WatchlistController::class, 'index']);
        Route::post('/watchlist', [WatchlistController::class, 'store']);
        Route::delete('/watchlist/{itemId}', [WatchlistController::class, 'destroy']);
        Route::get('/watchlist/check/{itemId}', [WatchlistController::class, 'check']);
        Route::get('/watchlist/count', [WatchlistController::class, 'count']);
        Route::post('/watchlist/toggle', [WatchlistController::class, 'toggle']);

        // Bid Dashboard routes
        Route::get('/bid-dashboard', [BidDashboardController::class, 'index']);
        Route::get('/bid-dashboard/active-bids', [BidDashboardController::class, 'activeBids']);
        Route::get('/bid-dashboard/bid-history', [BidDashboardController::class, 'bidHistory']);
        Route::get('/bid-dashboard/stats', [BidDashboardController::class, 'stats']);
    });

// Admin API routes (session-based authentication with staffMiddleware)
Route::prefix('admin')->middleware(['auth', 'staffMiddleware'])->group(function () {
    Route::get('/dashboard/stats', [AdminDashboardController::class, 'stats'])->name('api.admin.dashboard.stats');
    Route::get('/dashboard/recent-activity', [AdminDashboardController::class, 'recentActivity'])->name('api.admin.dashboard.recent-activity');
    Route::get('/dashboard/system-status', [AdminDashboardController::class, 'systemStatus'])->name('api.admin.dashboard.system-status');
    Route::get('/dashboard/chart-data', [AdminDashboardController::class, 'chartData'])->name('api.admin.dashboard.chart-data');
    Route::get('/notifications/expiring-items', [AdminDashboardController::class, 'expiringItems'])->name('api.admin.notifications.expiring-items');
    Route::get('/check-access', [AdminDashboardController::class, 'checkAccess'])->name('api.admin.check-access');
});

Route::get('/open-items', [PublicItemController::class, "ajaxItems"]);
Route::get('/open-item/{item}', [PublicItemController::class, "ajaxItem"]);
Route::get('/open-item/{item}/bids', [PublicItemController::class, 'bids']);

// Test route
Route::get('/test-cart', function() {
    return response()->json(['message' => 'Cart API is working', 'timestamp' => now()]);
});

// Enhanced Cart API Routes
Route::prefix('cart')->group(function () {
    // Get current cart
    Route::get('/', [App\Http\Controllers\Api\CartController::class, 'index']);

    // Cart item management
    Route::post('/add/{item}', [App\Http\Controllers\Api\CartController::class, 'addItem']);
    Route::delete('/remove/{item}', [App\Http\Controllers\Api\CartController::class, 'removeItem']);
    Route::put('/update/{item}', [App\Http\Controllers\Api\CartController::class, 'updateQuantity']);

    // Cart operations
    Route::delete('/clear', [App\Http\Controllers\Api\CartController::class, 'clear']);
    Route::post('/validate', [App\Http\Controllers\Api\CartController::class, 'validateCart']);
    Route::post('/bulk-update', [App\Http\Controllers\Api\CartController::class, 'bulkUpdate']);

    // Analytics and reporting
    Route::get('/analytics', [App\Http\Controllers\Api\CartController::class, 'analytics']);
});



// Clear cart after successful payment
Route::post('/cart/clear', function() {
    try {
        // Clear all cart sessions
        session()->forget(['cash', 'online', 'live']);

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to clear cart',
            'error' => $e->getMessage()
        ], 500);
    }
});

// Cart items API with detailed information
Route::get('/cart/items', function() {
    try {
        $cartDetails = \Facades\App\Cache\Repo::cartDetails();

        return response()->json([
            'items' => $cartDetails['items']->map(function($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'target_amount' => $item->target_amount,
                    'quantity' => $item->quantity ?? 1,
                    'subtotal' => $item->subtotal ?? ($item->target_amount * ($item->quantity ?? 1)),
                    'image' => $item->cropped ?? $item->image,
                    'auction_type' => [
                        'type' => $item->auctionType->type ?? 'unknown'
                    ]
                ];
            }),
            'cash' => $cartDetails['cash'],
            'online' => $cartDetails['online'],
            'live' => $cartDetails['live'],
            'summary' => $cartDetails['summary'],
            'debug' => [
                'cash_session' => session('cash'),
                'online_session' => session('online'),
                'live_session' => session('live'),
                'cash_quantities' => session('cash_quantities'),
                'online_quantities' => session('online_quantities'),
                'live_quantities' => session('live_quantities')
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'items' => [],
            'cash' => ['items' => [], 'count' => 0, 'total' => 0],
            'online' => ['items' => [], 'count' => 0, 'total' => 0],
            'live' => ['items' => [], 'count' => 0, 'total' => 0],
            'summary' => ['total_items' => 0, 'total_amount' => 0],
            'debug' => [
                'cash_session' => session('cash'),
                'online_session' => session('online'),
                'live_session' => session('live'),
                'cash_quantities' => session('cash_quantities'),
                'online_quantities' => session('online_quantities'),
                'live_quantities' => session('live_quantities')
            ]
        ]);
    }
});

// Update cart item quantity API
Route::patch('/cart/update/{item}', function(\App\Models\Item $item) {
    try {
        $type = $item->auctionType->type ?? '';
        if (!$type) {
            return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
        }

        $quantity = request()->input('quantity');
        if ($quantity === null) {
            return response()->json(['success' => false, 'message' => 'Quantity parameter required'], 400);
        }

        // Validate quantity
        if (!is_numeric($quantity) || $quantity < 0) {
            return response()->json(['success' => false, 'message' => 'Invalid quantity'], 400);
        }

        // Get current cart items and quantities
        $items = session($type) ?? [];
        $quantities = session($type . '_quantities') ?? [];

        // Check if item is in cart
        if (!in_array($item->id, $items)) {
            return response()->json(['success' => false, 'message' => 'Item not in cart'], 404);
        }

        if ($quantity == 0) {
            // Remove item completely
            $items = array_diff($items, [$item->id]);
            unset($quantities[$item->id]);
            $message = $item->name . ' removed from cart';
        } else {
            // Update quantity
            $quantities[$item->id] = (int)$quantity;
            $message = $item->name . ' quantity updated to ' . $quantity;
        }

        // Save to session
        session([$type => $items]);
        session([$type . '_quantities' => $quantities]);

        return response()->json([
            'success' => true,
            'message' => $message,
            'quantity' => $quantity == 0 ? 0 : $quantities[$item->id] ?? 0,
            'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
        ]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'message' => 'Failed to update cart item'], 500);
    }
});
