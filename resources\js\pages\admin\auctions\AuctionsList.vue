<template>
  <AdminListTemplate
    title="All Auctions"
    subtitle="View and manage all auction listings"
    :loading="loading"
    :error="error"
    :items="auctions"
    :columns="columns"
    :selected-items="selectedAuctions"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Create Auction"
    empty-state-title="No auctions found"
    empty-state-message="Get started by creating your first auction."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
  >
    <template #filters>
      <Select
        v-model="filters.status"
        placeholder="All Statuses"
        :options="statusOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.category"
        placeholder="All Categories"
        :options="categoryOptions"
        @change="applyFilters"
      />
      <Input
        v-model="filters.dateRange"
        type="date"
        placeholder="Date Range"
        @change="applyFilters"
      />
    </template>

    <template #listActions>
      <Button variant="outline" size="sm" @click="exportAuctions">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        Export
      </Button>
      <Button variant="outline" size="sm" @click="refreshData">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        Refresh
      </Button>
    </template>

    <template #bulkActions="{ selectedItems }">
      <Button variant="outline" size="sm" @click="bulkPublish(selectedItems)">
        Publish
      </Button>
      <Button variant="outline" size="sm" @click="bulkUnpublish(selectedItems)">
        Unpublish
      </Button>
      <Button variant="danger" size="sm" @click="bulkDelete(selectedItems)">
        Delete
      </Button>
    </template>

    <template #rows="{ items }">
      <tr v-for="auction in items" :key="auction.id" class="hover:bg-gray-50">
        <td class="px-6 py-4">
          <input
            type="checkbox"
            :checked="selectedAuctions.includes(auction.id)"
            @change="toggleSelection(auction.id)"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
        </td>
        <td class="px-6 py-4">
          <div class="flex items-center">
            <img
              v-if="auction.image"
              :src="auction.image"
              :alt="auction.title"
              class="w-10 h-10 rounded-lg object-cover mr-3"
            />
            <div>
              <div class="text-sm font-medium text-gray-900">{{ auction.title }}</div>
              <div class="text-sm text-gray-500">ID: {{ auction.id }}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4">
          <Badge :variant="getStatusVariant(auction.status)">
            {{ auction.status }}
          </Badge>
        </td>
        <td class="px-6 py-4 text-sm text-gray-900">
          {{ formatCurrency(auction.startingBid) }}
        </td>
        <td class="px-6 py-4 text-sm text-gray-900">
          {{ formatCurrency(auction.currentBid) }}
        </td>
        <td class="px-6 py-4 text-sm text-gray-500">
          {{ formatDate(auction.endDate) }}
        </td>
        <td class="px-6 py-4 text-sm text-gray-500">
          {{ auction.bidCount }}
        </td>
        <td class="px-6 py-4 text-right text-sm font-medium">
          <div class="flex items-center justify-end space-x-2">
            <Button
              variant="ghost"
              size="sm"
              @click="viewAuction(auction.id)"
            >
              View
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="editAuction(auction.id)"
            >
              Edit
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="deleteAuction(auction.id)"
              class="text-red-600 hover:text-red-800"
            >
              Delete
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Select, Input, Badge } from '@/components/ui';

// Router
const router = useRouter();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedAuctions = ref<number[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);

// Filters
const filters = ref({
  status: '',
  category: '',
  dateRange: '',
  search: ''
});

// Mock data - in real app, this would come from API/store
const auctions = ref([
  {
    id: 1,
    title: 'Vintage Watch Collection',
    status: 'active',
    startingBid: 100,
    currentBid: 1250,
    endDate: new Date('2024-01-15'),
    bidCount: 23,
    image: '/images/watch.jpg'
  },
  {
    id: 2,
    title: 'Art Deco Furniture Set',
    status: 'pending',
    startingBid: 500,
    currentBid: 850,
    endDate: new Date('2024-01-20'),
    bidCount: 12,
    image: '/images/furniture.jpg'
  },
  {
    id: 3,
    title: 'Classic Car Parts',
    status: 'ended',
    startingBid: 200,
    currentBid: 2100,
    endDate: new Date('2024-01-10'),
    bidCount: 45,
    image: '/images/car-parts.jpg'
  }
]);

// Table columns
const columns = [
  { key: 'title', label: 'Auction', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'startingBid', label: 'Starting Bid', sortable: true },
  { key: 'currentBid', label: 'Current Bid', sortable: true },
  { key: 'endDate', label: 'End Date', sortable: true },
  { key: 'bidCount', label: 'Bids', sortable: true }
];

// Filter options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'pending', label: 'Pending' },
  { value: 'ended', label: 'Ended' },
  { value: 'cancelled', label: 'Cancelled' }
];

const categoryOptions = [
  { value: '', label: 'All Categories' },
  { value: 'antiques', label: 'Antiques' },
  { value: 'art', label: 'Art' },
  { value: 'collectibles', label: 'Collectibles' },
  { value: 'jewelry', label: 'Jewelry' }
];

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'pending': return 'warning';
    case 'ended': return 'info';
    case 'cancelled': return 'error';
    default: return 'default';
  }
};

const handleCreate = () => {
  router.push('/admin-spa/auctions/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAuctions();
};

const handleSelectAll = (selected: boolean) => {
  if (selected) {
    selectedAuctions.value = auctions.value.map(a => a.id);
  } else {
    selectedAuctions.value = [];
  }
};

const toggleSelection = (id: number) => {
  const index = selectedAuctions.value.indexOf(id);
  if (index > -1) {
    selectedAuctions.value.splice(index, 1);
  } else {
    selectedAuctions.value.push(id);
  }
};

const applyFilters = () => {
  // Implement filter logic
  fetchAuctions();
};

const fetchAuctions = async () => {
  loading.value = true;
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    // In real app, fetch filtered/paginated data
    totalItems.value = auctions.value.length;
    totalPages.value = Math.ceil(totalItems.value / perPage.value);
  } catch (err) {
    error.value = 'Failed to fetch auctions';
  } finally {
    loading.value = false;
  }
};

const refreshData = () => {
  fetchAuctions();
};

const exportAuctions = () => {
  // Implement export functionality
  console.log('Exporting auctions...');
};

const viewAuction = (id: number) => {
  router.push(`/admin-spa/auctions/view/${id}`);
};

const editAuction = (id: number) => {
  router.push(`/admin-spa/auctions/edit/${id}`);
};

const deleteAuction = (id: number) => {
  // Implement delete functionality
  console.log('Delete auction:', id);
};

const bulkPublish = (items: any[]) => {
  console.log('Bulk publish:', items);
};

const bulkUnpublish = (items: any[]) => {
  console.log('Bulk unpublish:', items);
};

const bulkDelete = (items: any[]) => {
  console.log('Bulk delete:', items);
};

// Lifecycle
onMounted(() => {
  console.log('AuctionsList component mounted - Vue 3 is working!');
  console.log('Current user:', (window as any).user);
  console.log('CSRF Token:', (window as any).csrfToken);
  fetchAuctions();
});
</script>
