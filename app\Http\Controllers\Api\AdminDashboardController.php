<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Item;
use App\Models\Auction;
use App\Models\Transaction;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Get admin dashboard statistics
     */
    public function stats(Request $request)
    {
        // Ensure user has admin access
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $dateFrom = $request->get('date_from', Carbon::now()->subDays(30)->toDateString());
        $dateTo = $request->get('date_to', Carbon::now()->toDateString());
        $branchId = $request->get('branch_id', auth()->user()->branch_id);

        // Base query with branch filtering if not super admin
        $baseQuery = function($model) use ($branchId) {
            $query = $model::query();
            if (!auth()->user()->isSuperAdmin() && $branchId) {
                $query->where('branch_id', $branchId);
            }
            return $query;
        };

        // Get current period stats
        $currentStats = $this->getPeriodStats($baseQuery, $dateFrom, $dateTo);
        
        // Get previous period stats for comparison
        $previousDateFrom = Carbon::parse($dateFrom)->subDays(Carbon::parse($dateTo)->diffInDays(Carbon::parse($dateFrom)))->toDateString();
        $previousDateTo = Carbon::parse($dateFrom)->subDay()->toDateString();
        $previousStats = $this->getPeriodStats($baseQuery, $previousDateFrom, $previousDateTo);

        // Calculate percentage changes
        $stats = [
            'activeAuctions' => $currentStats['activeAuctions'],
            'activeAuctionsChange' => $this->calculatePercentageChange($previousStats['activeAuctions'], $currentStats['activeAuctions']),
            'totalRevenue' => $currentStats['totalRevenue'],
            'revenueChange' => $this->calculatePercentageChange($previousStats['totalRevenue'], $currentStats['totalRevenue']),
            'totalItems' => $currentStats['totalItems'],
            'itemsChange' => $this->calculatePercentageChange($previousStats['totalItems'], $currentStats['totalItems']),
            'totalUsers' => $currentStats['totalUsers'],
            'usersChange' => $this->calculatePercentageChange($previousStats['totalUsers'], $currentStats['totalUsers']),
            'totalBids' => $currentStats['totalBids'],
            'bidsChange' => $this->calculatePercentageChange($previousStats['totalBids'], $currentStats['totalBids']),
            'conversionRate' => $currentStats['conversionRate'],
            'conversionRateChange' => $this->calculatePercentageChange($previousStats['conversionRate'], $currentStats['conversionRate']),
        ];

        return response()->json($stats);
    }

    /**
     * Get recent activity for dashboard
     */
    public function recentActivity(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $limit = $request->get('limit', 10);
        $branchId = $request->get('branch_id', auth()->user()->branch_id);

        $activities = collect();

        // Recent auctions
        $recentAuctions = Auction::with(['item', 'user'])
            ->when(!auth()->user()->isSuperAdmin() && $branchId, function($query) use ($branchId) {
                return $query->where('branch_id', $branchId);
            })
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($auction) {
                return [
                    'id' => 'auction_' . $auction->id,
                    'type' => $auction->closed_by ? 'auction_ended' : 'bid_placed',
                    'title' => $auction->closed_by ? 'Auction Won' : 'New Bid Placed',
                    'description' => 'Bid of $' . number_format($auction->bid_amount, 2) . ' on ' . ($auction->item->title ?? 'Item'),
                    'timestamp' => $auction->created_at->toISOString(),
                    'user' => $auction->user ? [
                        'id' => $auction->user->id,
                        'name' => $auction->user->name,
                        'avatar' => $auction->user->avatar ?? null
                    ] : null,
                    'metadata' => [
                        'auction_id' => $auction->id,
                        'item_id' => $auction->item_id,
                        'bid_amount' => $auction->bid_amount
                    ]
                ];
            });

        $activities = $activities->merge($recentAuctions);

        // Recent user registrations
        $recentUsers = User::whereHas('roles', function($query) {
                $query->where('name', 'customer');
            })
            ->when(!auth()->user()->isSuperAdmin() && $branchId, function($query) use ($branchId) {
                return $query->where('branch_id', $branchId);
            })
            ->latest()
            ->limit(3)
            ->get()
            ->map(function($user) {
                return [
                    'id' => 'user_' . $user->id,
                    'type' => 'user_registered',
                    'title' => 'New User Registration',
                    'description' => $user->name . ' joined the platform',
                    'timestamp' => $user->created_at->toISOString(),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'avatar' => $user->avatar ?? null
                    ],
                    'metadata' => [
                        'user_id' => $user->id
                    ]
                ];
            });

        $activities = $activities->merge($recentUsers);

        // Sort by timestamp and limit
        $activities = $activities->sortByDesc('timestamp')->take($limit)->values();

        return response()->json($activities);
    }

    /**
     * Get system status information
     */
    public function systemStatus(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $status = [
            [
                'name' => 'Database',
                'status' => 'healthy',
                'message' => 'All connections active',
                'lastCheck' => now()->toISOString()
            ],
            [
                'name' => 'Cache',
                'status' => 'healthy',
                'message' => 'Redis operational',
                'lastCheck' => now()->toISOString()
            ],
            [
                'name' => 'Storage',
                'status' => 'healthy',
                'message' => 'File system accessible',
                'lastCheck' => now()->toISOString()
            ],
            [
                'name' => 'Email',
                'status' => 'healthy',
                'message' => 'SMTP configured',
                'lastCheck' => now()->toISOString()
            ]
        ];

        return response()->json($status);
    }

    /**
     * Get chart data for dashboard
     */
    public function chartData(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $type = $request->get('type', 'revenue');
        $period = $request->get('period', '30'); // days
        $branchId = $request->get('branch_id', auth()->user()->branch_id);

        $endDate = Carbon::now();
        $startDate = $endDate->copy()->subDays($period);

        $data = [];
        $labels = [];

        // Generate date range
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $labels[] = $date->format('M j');
            
            switch ($type) {
                case 'revenue':
                    $value = Transaction::where('created_at', '>=', $date->startOfDay())
                        ->where('created_at', '<=', $date->endOfDay())
                        ->when(!auth()->user()->isSuperAdmin() && $branchId, function($query) use ($branchId) {
                            return $query->where('branch_id', $branchId);
                        })
                        ->sum('amount');
                    break;
                    
                case 'auctions':
                    $value = Auction::where('created_at', '>=', $date->startOfDay())
                        ->where('created_at', '<=', $date->endOfDay())
                        ->when(!auth()->user()->isSuperAdmin() && $branchId, function($query) use ($branchId) {
                            return $query->where('branch_id', $branchId);
                        })
                        ->count();
                    break;
                    
                case 'users':
                    $value = User::where('created_at', '>=', $date->startOfDay())
                        ->where('created_at', '<=', $date->endOfDay())
                        ->when(!auth()->user()->isSuperAdmin() && $branchId, function($query) use ($branchId) {
                            return $query->where('branch_id', $branchId);
                        })
                        ->count();
                    break;
                    
                default:
                    $value = 0;
            }
            
            $data[] = $value;
        }

        return response()->json([
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => ucfirst($type),
                    'data' => $data,
                    'borderColor' => $this->getChartColor($type),
                    'backgroundColor' => $this->getChartColor($type, 0.1),
                    'fill' => true
                ]
            ]
        ]);
    }

    /**
     * Get expiring items notifications
     */
    public function expiringItems(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $days = $request->get('days', 7);
        $branchId = $request->get('branch_id', auth()->user()->branch_id);

        $expiringItems = Item::with(['auctions' => function($query) {
                $query->whereNull('closed_by')->latest();
            }])
            ->whereHas('auctions', function($query) use ($days) {
                $query->whereNull('closed_by')
                      ->where('date_to', '<=', Carbon::now()->addDays($days))
                      ->where('date_to', '>', Carbon::now());
            })
            ->when(!auth()->user()->isSuperAdmin() && $branchId, function($query) use ($branchId) {
                return $query->where('branch_id', $branchId);
            })
            ->get()
            ->map(function($item) {
                $auction = $item->auctions->first();
                return [
                    'id' => $item->id,
                    'title' => $item->title,
                    'expires_at' => $auction ? $auction->date_to : null,
                    'current_bid' => $auction ? $auction->bid_amount : 0,
                    'bid_count' => $item->auctions->count()
                ];
            });

        return response()->json([
            'count' => $expiringItems->count(),
            'items' => $expiringItems
        ]);
    }

    /**
     * Get period statistics
     */
    private function getPeriodStats($baseQuery, $dateFrom, $dateTo)
    {
        $activeAuctions = $baseQuery(Auction::class)
            ->whereNull('closed_by')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        $totalRevenue = $baseQuery(Transaction::class)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->sum('amount');

        $totalItems = $baseQuery(Item::class)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        $totalUsers = $baseQuery(User::class)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        $totalBids = $baseQuery(Auction::class)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        $conversionRate = $totalBids > 0 ? ($activeAuctions / $totalBids) * 100 : 0;

        return [
            'activeAuctions' => $activeAuctions,
            'totalRevenue' => $totalRevenue,
            'totalItems' => $totalItems,
            'totalUsers' => $totalUsers,
            'totalBids' => $totalBids,
            'conversionRate' => round($conversionRate, 2)
        ];
    }

    /**
     * Calculate percentage change between two values
     */
    private function calculatePercentageChange($previous, $current)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 2);
    }

    /**
     * Get chart color based on type
     */
    private function getChartColor($type, $opacity = 1)
    {
        $colors = [
            'revenue' => "rgba(34, 197, 94, $opacity)",
            'auctions' => "rgba(59, 130, 246, $opacity)",
            'users' => "rgba(168, 85, 247, $opacity)",
            'items' => "rgba(245, 158, 11, $opacity)"
        ];

        return $colors[$type] ?? "rgba(107, 114, 128, $opacity)";
    }

    /**
     * Check admin access permissions
     */
    public function checkAccess()
    {
        $user = auth()->user();

        if (!$user) {
            return response()->json(['hasAccess' => false, 'reason' => 'Not authenticated'], 401);
        }

        // Use the same logic as StaffMiddleware
        if ($user->isCustomer() || $user->isSupplier()) {
            return response()->json(['hasAccess' => false, 'reason' => 'Customer or supplier access denied'], 403);
        }

        return response()->json([
            'hasAccess' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $user->roles ?? [],
                'permissions' => $user->permissions ?? []
            ]
        ]);
    }
}
