<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-green-600 mb-6">
          ✅ Vue 3 is Working!
        </h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Vue 3 Features Test -->
          <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Vue 3 Features</h2>
            
            <div class="p-4 bg-blue-50 rounded-lg">
              <h3 class="font-medium text-blue-800">Composition API</h3>
              <p class="text-blue-600">Counter: {{ counter }}</p>
              <button 
                @click="increment" 
                class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Increment
              </button>
            </div>
            
            <div class="p-4 bg-green-50 rounded-lg">
              <h3 class="font-medium text-green-800">Reactive Data</h3>
              <input 
                v-model="message" 
                placeholder="Type something..."
                class="w-full p-2 border rounded"
              />
              <p class="mt-2 text-green-600">You typed: {{ message }}</p>
            </div>
            
            <div class="p-4 bg-purple-50 rounded-lg">
              <h3 class="font-medium text-purple-800">Computed Properties</h3>
              <p class="text-purple-600">Doubled counter: {{ doubledCounter }}</p>
              <p class="text-purple-600">Message length: {{ messageLength }}</p>
            </div>
          </div>
          
          <!-- Authentication & Environment -->
          <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-800">Environment Info</h2>
            
            <div class="p-4 bg-yellow-50 rounded-lg">
              <h3 class="font-medium text-yellow-800">User Authentication</h3>
              <pre class="text-sm text-yellow-700 mt-2">{{ JSON.stringify(user, null, 2) }}</pre>
            </div>
            
            <div class="p-4 bg-indigo-50 rounded-lg">
              <h3 class="font-medium text-indigo-800">CSRF Token</h3>
              <p class="text-indigo-600 text-sm break-all">{{ csrfToken }}</p>
            </div>
            
            <div class="p-4 bg-pink-50 rounded-lg">
              <h3 class="font-medium text-pink-800">Branches Data</h3>
              <pre class="text-sm text-pink-700 mt-2">{{ JSON.stringify(branches, null, 2) }}</pre>
            </div>
          </div>
        </div>
        
        <!-- Navigation Test -->
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Navigation Test</h2>
          <div class="space-x-4">
            <button 
              @click="goToDashboard"
              class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Go to Dashboard
            </button>
            <button 
              @click="goToAuctions"
              class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Go to Auctions List
            </button>
          </div>
        </div>
        
        <!-- Component Test -->
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Component Test</h2>
          <Button variant="primary" @click="testAlert">Test Button Component</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Button } from '@/components/ui';

// Router
const router = useRouter();

// Reactive state
const counter = ref(0);
const message = ref('');

// Data from window
const user = ref((window as any).user || null);
const csrfToken = ref((window as any).csrfToken || '');
const branches = ref((window as any).branches || []);

// Computed properties
const doubledCounter = computed(() => counter.value * 2);
const messageLength = computed(() => message.value.length);

// Methods
const increment = () => {
  counter.value++;
};

const goToDashboard = () => {
  router.push('/admin-spa/dashboard');
};

const goToAuctions = () => {
  router.push('/admin-spa/auctions/list');
};

const testAlert = () => {
  alert('Button component is working!');
};

// Lifecycle
onMounted(() => {
  console.log('Vue 3 Test Component mounted successfully!');
  console.log('User:', user.value);
  console.log('CSRF Token:', csrfToken.value);
  console.log('Branches:', branches.value);
});
</script>
