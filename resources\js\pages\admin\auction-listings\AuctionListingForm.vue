<template>
  <AdminFormTemplate
    :title="isEditing ? 'Edit Auction Listing' : 'Create Auction Listing'"
    :subtitle="isEditing ? 'Update auction listing details' : 'Create a new auction listing'"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
    @submit="handleSubmit"
    @cancel="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Auction Name -->
          <div class="md:col-span-2">
            <FormField
              v-model="form.name"
              label="Auction Name"
              placeholder="Enter auction name"
              :error="errors.name"
            />
          </div>

          <!-- Code -->
          <FormField
            v-model="form.code"
            label="Auction Code"
            placeholder="Enter auction code"
            :error="errors.code"
          />

          <!-- Auction Type -->
          <FormField
            v-model="form.auction_type_id"
            label="Auction Type"
            type="select"
            placeholder="Select auction type"
            :options="auctionTypeOptions"
            :error="errors.auction_type_id"
            required
          />

          <!-- Item -->
          <FormField
            v-model="form.item_id"
            label="Item"
            type="select"
            placeholder="Select item"
            :options="itemOptions"
            :error="errors.item_id"
            required
          />

          <!-- Branch -->
          <FormField
            v-model="form.branch_id"
            label="Branch"
            type="select"
            placeholder="Select branch"
            :options="branchOptions"
            :error="errors.branch_id"
          />

          <!-- Description -->
          <div class="md:col-span-2">
            <FormField
              v-model="form.description"
              label="Description"
              type="textarea"
              placeholder="Enter auction description"
              :error="errors.description"
              rows="4"
            />
          </div>
        </div>
      </Card>

      <!-- Bidding Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Bidding Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Starting Bid Amount -->
          <FormField
            v-model="form.bid_amount"
            label="Starting Bid Amount"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            :error="errors.bid_amount"
          />

          <!-- Owner/Bidder -->
          <FormField
            v-model="form.user_id"
            label="Owner/Bidder"
            type="select"
            placeholder="Select owner"
            :options="userOptions"
            :error="errors.user_id"
          />
        </div>
      </Card>

      <!-- Schedule -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Start Date -->
          <FormField
            v-model="form.date_from"
            label="Start Date & Time"
            type="datetime-local"
            :error="errors.date_from"
          />

          <!-- End Date -->
          <FormField
            v-model="form.date_to"
            label="End Date & Time"
            type="datetime-local"
            :error="errors.date_to"
          />
        </div>
      </Card>

      <!-- Selected Item Preview -->
      <Card v-if="selectedItem" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Selected Item Preview</h3>
        
        <div class="flex items-start space-x-4">
          <img
            :src="selectedItem.image || '/img/product.jpeg'"
            :alt="selectedItem.name"
            class="w-24 h-24 rounded-lg object-cover"
          />
          <div class="flex-1">
            <h4 class="text-lg font-medium text-gray-900">{{ selectedItem.name }}</h4>
            <p class="text-sm text-gray-600 mt-1">Ref: {{ selectedItem.reference_number || 'N/A' }}</p>
            <p class="text-sm text-gray-600">Target: {{ formatCurrency(selectedItem.target_amount) }}</p>
            <p v-if="selectedItem.description" class="text-sm text-gray-500 mt-2 line-clamp-2">
              {{ selectedItem.description }}
            </p>
            <div class="flex items-center space-x-2 mt-2">
              <AdminBadge
                :variant="selectedItem.closed_by ? 'success' : 'warning'"
                size="sm"
              >
                {{ selectedItem.closed_by ? 'Sold' : 'Available' }}
              </AdminBadge>
              <AdminBadge
                v-if="selectedItem.auctionType"
                :variant="getAuctionTypeBadgeVariant(selectedItem.auctionType.type)"
                size="sm"
              >
                {{ selectedItem.auctionType.name }}
              </AdminBadge>
            </div>
          </div>
        </div>
      </Card>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          @click="handleCancel"
          :disabled="submitting"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          :loading="submitting"
          :disabled="!isFormValid"
        >
          {{ isEditing ? 'Update Auction' : 'Create Auction' }}
        </Button>
      </div>
    </form>
  </AdminFormTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminFormTemplate, AdminBadge } from '@/components/admin/templates';
import { Card, Button, FormField } from '@/components/ui';
import { useAdminAuctions } from '@/stores/admin/auctions';
import { useAdminAuctionTypes } from '@/stores/admin/auctionTypes';
import { useAdminItems } from '@/stores/admin/items';
import { useAdminBranches } from '@/stores/admin/branches';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();
const route = useRoute();

// Stores
const auctionsStore = useAdminAuctions();
const auctionTypesStore = useAdminAuctionTypes();
const itemsStore = useAdminItems();
const branchesStore = useAdminBranches();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const submitting = ref(false);
const error = ref<string | null>(null);
const users = ref<any[]>([]);

// Form data
const form = reactive({
  name: '',
  code: '',
  auction_type_id: '',
  item_id: '',
  branch_id: '',
  description: '',
  bid_amount: '',
  user_id: '',
  date_from: '',
  date_to: ''
});

// Form errors
const errors = reactive({
  name: '',
  code: '',
  auction_type_id: '',
  item_id: '',
  branch_id: '',
  description: '',
  bid_amount: '',
  user_id: '',
  date_from: '',
  date_to: ''
});

// Computed
const isEditing = computed(() => !!route.params.id);
const auctionId = computed(() => route.params.id ? parseInt(route.params.id as string) : null);

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/admin-spa' },
  { label: 'Auction Listings', href: '/admin-spa/auction-listings/list' },
  { label: isEditing.value ? 'Edit Auction' : 'Create Auction' }
]);

const isFormValid = computed(() => {
  return form.auction_type_id !== '' && form.item_id !== '';
});

const selectedItem = computed(() => {
  if (!form.item_id) return null;
  return itemsStore.itemsList.find(item => item.id.toString() === form.item_id);
});

// Options for dropdowns
const auctionTypeOptions = computed(() => [
  ...auctionTypesStore.auctionTypes.map(type => ({
    label: type.name,
    value: type.id.toString()
  }))
]);

const itemOptions = computed(() => [
  ...itemsStore.itemsList
    .filter(item => !item.closed_by) // Only show available items
    .map(item => ({
      label: `${item.name} (${item.reference_number || 'No ref'})`,
      value: item.id.toString()
    }))
]);

const branchOptions = computed(() => [
  ...branchesStore.branches.map(branch => ({
    label: branch.name,
    value: branch.id.toString()
  }))
]);

const userOptions = computed(() => [
  ...users.value.map(user => ({
    label: `${user.name} (${user.email})`,
    value: user.id.toString()
  }))
]);

// Methods
const loadAuction = async () => {
  if (!auctionId.value) return;

  loading.value = true;
  try {
    const auction = await auctionsStore.fetchAuction(auctionId.value);
    
    // Populate form with auction data
    form.name = auction.name || '';
    form.code = auction.code || '';
    form.auction_type_id = auction.auction_type_id?.toString() || '';
    form.item_id = auction.item_id?.toString() || '';
    form.branch_id = auction.branch_id?.toString() || '';
    form.description = auction.description || '';
    form.bid_amount = auction.bid_amount?.toString() || '';
    form.user_id = auction.user_id?.toString() || '';
    form.date_from = auction.date_from ? formatDateTimeLocal(auction.date_from) : '';
    form.date_to = auction.date_to ? formatDateTimeLocal(auction.date_to) : '';
  } catch (err) {
    error.value = 'Failed to load auction';
    showNotification('Failed to load auction', 'error');
  } finally {
    loading.value = false;
  }
};

const loadUsers = async () => {
  try {
    const response = await axios.get('/api/users');
    users.value = response.data.data || response.data;
  } catch (err) {
    console.error('Failed to load users:', err);
  }
};

const handleSubmit = async () => {
  if (!isFormValid.value) return;

  submitting.value = true;
  clearErrors();

  try {
    const auctionData = {
      name: form.name || undefined,
      code: form.code || undefined,
      auction_type_id: parseInt(form.auction_type_id),
      item_id: parseInt(form.item_id),
      branch_id: form.branch_id ? parseInt(form.branch_id) : undefined,
      description: form.description || undefined,
      bid_amount: form.bid_amount ? parseFloat(form.bid_amount) : undefined,
      user_id: form.user_id ? parseInt(form.user_id) : undefined,
      date_from: form.date_from || undefined,
      date_to: form.date_to || undefined
    };

    if (isEditing.value && auctionId.value) {
      await auctionsStore.updateAuction({ id: auctionId.value, ...auctionData });
      showNotification('Auction updated successfully', 'success');
    } else {
      await auctionsStore.createAuction(auctionData);
      showNotification('Auction created successfully', 'success');
    }

    router.push('/admin-spa/auction-listings/list');
  } catch (err: any) {
    if (err.response?.data?.errors) {
      Object.assign(errors, err.response.data.errors);
    } else {
      error.value = err.response?.data?.message || 'Failed to save auction';
      showNotification('Failed to save auction', 'error');
    }
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  router.push('/admin-spa/auction-listings/list');
};

const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });
  error.value = null;
};

// Utility methods
const getAuctionTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDateTimeLocal = (dateString: string) => {
  const date = new Date(dateString);
  return date.toISOString().slice(0, 16);
};

// Watch for pre-selected item from query params
watch(() => route.query.item_id, (itemId) => {
  if (itemId && !isEditing.value) {
    form.item_id = itemId.toString();
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  await Promise.all([
    auctionTypesStore.fetchAuctionTypes(),
    itemsStore.fetchItems({ per_page: 100 }), // Load more items for selection
    branchesStore.fetchBranches(),
    loadUsers()
  ]);

  if (isEditing.value) {
    await loadAuction();
  }
});
</script>
