import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';

export interface AdminState {
  sidebarCollapsed: boolean;
  mobileMenuOpen: boolean;
  notificationsOpen: boolean;
  openMenus: Record<string, boolean>;
  preferences: {
    theme: 'light' | 'dark';
    density: 'comfortable' | 'compact';
    language: string;
  };
}

export const useAdminStore = defineStore('admin', () => {
  // State
  const sidebarCollapsed = ref(false);
  const mobileMenuOpen = ref(false);
  const notificationsOpen = ref(false);
  
  const openMenus = reactive({
    auctions: false,
    sales: false,
    items: false,
    users: false,
    financial: false,
    reports: false,
    settings: false
  });

  const preferences = reactive({
    theme: 'light' as const,
    density: 'comfortable' as const,
    language: 'en'
  });

  // Getters
  const isInitialized = ref(false);

  // Actions
  const initialize = () => {
    // Load preferences from localStorage
    const savedPreferences = localStorage.getItem('admin-preferences');
    if (savedPreferences) {
      try {
        const parsed = JSON.parse(savedPreferences);
        Object.assign(preferences, parsed);
      } catch (error) {
        console.error('Failed to parse admin preferences:', error);
      }
    }

    // Load sidebar state from localStorage
    const savedSidebarState = localStorage.getItem('admin-sidebar-collapsed');
    if (savedSidebarState !== null) {
      sidebarCollapsed.value = JSON.parse(savedSidebarState);
    }

    // Load open menus state from localStorage
    const savedOpenMenus = localStorage.getItem('admin-open-menus');
    if (savedOpenMenus) {
      try {
        const parsed = JSON.parse(savedOpenMenus);
        Object.assign(openMenus, parsed);
      } catch (error) {
        console.error('Failed to parse admin open menus:', error);
      }
    }

    isInitialized.value = true;
  };

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
    
    // Close all menus when collapsing
    if (sidebarCollapsed.value) {
      Object.keys(openMenus).forEach(key => {
        openMenus[key] = false;
      });
    }

    // Save to localStorage
    localStorage.setItem('admin-sidebar-collapsed', JSON.stringify(sidebarCollapsed.value));
  };

  const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;
    
    // Prevent body scroll when mobile menu is open
    if (mobileMenuOpen.value) {
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }
  };

  const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
    document.body.classList.remove('overflow-hidden');
  };

  const toggleNotifications = () => {
    notificationsOpen.value = !notificationsOpen.value;
  };

  const closeNotifications = () => {
    notificationsOpen.value = false;
  };

  const toggleMenu = (menuKey: string) => {
    if (sidebarCollapsed.value) return;
    
    openMenus[menuKey] = !openMenus[menuKey];
    
    // Save to localStorage
    localStorage.setItem('admin-open-menus', JSON.stringify(openMenus));
  };

  const closeAllMenus = () => {
    Object.keys(openMenus).forEach(key => {
      openMenus[key] = false;
    });
    localStorage.setItem('admin-open-menus', JSON.stringify(openMenus));
  };

  const updatePreferences = (newPreferences: Partial<typeof preferences>) => {
    Object.assign(preferences, newPreferences);
    localStorage.setItem('admin-preferences', JSON.stringify(preferences));
  };

  const reset = () => {
    sidebarCollapsed.value = false;
    mobileMenuOpen.value = false;
    notificationsOpen.value = false;
    closeAllMenus();
    
    // Reset preferences to defaults
    preferences.theme = 'light';
    preferences.density = 'comfortable';
    preferences.language = 'en';
    
    // Clear localStorage
    localStorage.removeItem('admin-preferences');
    localStorage.removeItem('admin-sidebar-collapsed');
    localStorage.removeItem('admin-open-menus');
  };

  return {
    // State
    sidebarCollapsed,
    mobileMenuOpen,
    notificationsOpen,
    openMenus,
    preferences,
    isInitialized,

    // Actions
    initialize,
    toggleSidebar,
    toggleMobileMenu,
    closeMobileMenu,
    toggleNotifications,
    closeNotifications,
    toggleMenu,
    closeAllMenus,
    updatePreferences,
    reset
  };
});

export type AdminStore = ReturnType<typeof useAdminStore>;

// Re-export all admin stores
export { useAdminDashboard } from './dashboard';
export { useAdminUsers } from './users';
export { useAdminAuctions } from './auctions';
export { useAdminSettings } from './settings';

// Re-export store types
export type { AdminDashboardStore } from './dashboard';
export type { AdminUsersStore } from './users';
export type { AdminAuctionsStore } from './auctions';
export type { AdminSettingsStore } from './settings';
