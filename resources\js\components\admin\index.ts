// Layout Components
export { default as AdminLayout } from './layout/AdminLayout.vue';
export { default as AdminSidebar } from './layout/AdminSidebar.vue';
export { default as AdminHeader } from './layout/AdminHeader.vue';
export { default as AdminContainer } from './layout/AdminContainer.vue';

// Navigation Components
export { default as AdminNavigation } from './navigation/AdminNavigation.vue';
export { default as AdminMenuItem } from './navigation/AdminMenuItem.vue';
export { default as AdminMobileMenu } from './navigation/AdminMobileMenu.vue';
export { default as MobileMenuItem } from './navigation/MobileMenuItem.vue';

// Page Templates
export {
  AdminPageTemplate,
  AdminListTemplate,
  AdminFormTemplate,
  AdminDetailTemplate
} from './templates';

// Admin UI Components
export {
  AdminStatsCard,
  AdminDataTable,
  AdminForm,
  AdminFormField,
  AdminFileUpload,
  AdminRichTextEditor,
  AdminChart,
  AdminModal,
  AdminButton,
  AdminBadge
} from './ui';

// Re-export admin stores and composables
export {
  useAdminStore,
  useAdminDashboard,
  useAdminUsers,
  useAdminAuctions,
  useAdminSettings
} from '@/stores/admin';
export { useAdminNavigation } from '@/composables/admin/useAdminNavigation';

// Types
export type {
  AdminState,
  AdminStore,
  AdminDashboardStore,
  AdminUsersStore,
  AdminAuctionsStore,
  AdminSettingsStore
} from '@/stores/admin';
export type { NavigationItem } from '@/composables/admin/useAdminNavigation';
export type {
  Breadcrumb,
  Column,
  FormSection,
  DetailSection,
  Status
} from './templates';
