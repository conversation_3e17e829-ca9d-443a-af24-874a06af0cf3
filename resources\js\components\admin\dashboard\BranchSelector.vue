<template>
  <div class="relative">
    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
      Branch
    </label>
    <div class="relative">
      <select
        v-model="selectedBranch"
        @change="handleBranchChange"
        class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
        :disabled="loading || !canChangeBranch"
      >
        <option value="">All Branches</option>
        <option
          v-for="branch in branches"
          :key="branch.id"
          :value="branch.id"
        >
          {{ branch.name }}
        </option>
      </select>
      
      <!-- Loading spinner -->
      <div
        v-if="loading"
        class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
      >
        <svg class="animate-spin h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      
      <!-- Dropdown arrow -->
      <div
        v-else
        class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
      >
        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>
    
    <!-- Branch info -->
    <div v-if="selectedBranchInfo" class="mt-2 text-xs text-gray-500 dark:text-gray-400">
      <div class="flex items-center space-x-2">
        <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <span>{{ selectedBranchInfo.address || 'No address' }}</span>
      </div>
      <div v-if="selectedBranchInfo.phone" class="flex items-center space-x-2 mt-1">
        <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
        <span>{{ selectedBranchInfo.phone }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';

interface Branch {
  id: number;
  name: string;
  address?: string;
  phone?: string;
  active: boolean;
}

interface Props {
  modelValue?: number | string;
  loading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: number | string): void;
  (e: 'change', branch: Branch | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  loading: false
});

const emit = defineEmits<Emits>();

// State
const branches = ref<Branch[]>([]);
const selectedBranch = ref<number | string>(props.modelValue);
const authStore = useAuthStore();

// Computed
const canChangeBranch = computed(() => {
  return authStore.user?.isSuperAdmin || false;
});

const selectedBranchInfo = computed(() => {
  if (!selectedBranch.value) return null;
  return branches.value.find(b => b.id === Number(selectedBranch.value)) || null;
});

// Methods
const fetchBranches = async () => {
  try {
    // Use global branches data if available
    if ((window as any).branches) {
      branches.value = (window as any).branches.filter((b: Branch) => b.active);
      return;
    }

    // Fallback to API call
    const response = await fetch('/api/branches', {
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (response.ok) {
      const data = await response.json();
      branches.value = data.data || data;
    }
  } catch (error) {
    console.error('Failed to fetch branches:', error);
  }
};

const handleBranchChange = () => {
  const branch = selectedBranchInfo.value;
  emit('update:modelValue', selectedBranch.value);
  emit('change', branch);
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  selectedBranch.value = newValue;
});

// Lifecycle
onMounted(() => {
  fetchBranches();
  
  // Set default branch for non-super admin users
  if (!canChangeBranch.value && authStore.user?.branch_id) {
    selectedBranch.value = authStore.user.branch_id;
    handleBranchChange();
  }
});
</script>

<style scoped>
/* Custom select styling */
select:disabled {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed;
}

select:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}
</style>
