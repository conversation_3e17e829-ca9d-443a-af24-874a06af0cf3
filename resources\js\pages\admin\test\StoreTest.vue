<template>
  <AdminPageTemplate
    title="Store Testing"
    subtitle="Test admin stores functionality"
  >
    <div class="space-y-8">
      <!-- Dashboard Store Test -->
      <Card>
        <template #header>
          <h3 class="text-lg font-semibold">Dashboard Store Test</h3>
        </template>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-sm text-blue-600">Active Auctions</div>
              <div class="text-2xl font-bold text-blue-900">{{ dashboardStore.stats.activeAuctions }}</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-sm text-green-600">Total Revenue</div>
              <div class="text-2xl font-bold text-green-900">{{ dashboardStore.totalRevenueCurrency }}</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-sm text-yellow-600">Total Items</div>
              <div class="text-2xl font-bold text-yellow-900">{{ dashboardStore.stats.totalItems }}</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
              <div class="text-sm text-purple-600">Total Users</div>
              <div class="text-2xl font-bold text-purple-900">{{ dashboardStore.stats.totalUsers }}</div>
            </div>
          </div>
          
          <div class="flex gap-2">
            <Button @click="testDashboardStore" :loading="dashboardStore.isLoading">
              Fetch Dashboard Data
            </Button>
            <Button @click="dashboardStore.refreshStats" variant="outline">
              Refresh Stats
            </Button>
          </div>
          
          <div v-if="dashboardStore.error" class="text-red-600 text-sm">
            Error: {{ dashboardStore.error }}
          </div>
        </div>
      </Card>

      <!-- Users Store Test -->
      <Card>
        <template #header>
          <h3 class="text-lg font-semibold">Users Store Test</h3>
        </template>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-sm text-blue-600">Total Users</div>
              <div class="text-2xl font-bold text-blue-900">{{ usersStore.userStats.total }}</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-sm text-green-600">Active Users</div>
              <div class="text-2xl font-bold text-green-900">{{ usersStore.userStats.active }}</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-sm text-yellow-600">New This Month</div>
              <div class="text-2xl font-bold text-yellow-900">{{ usersStore.userStats.newThisMonth }}</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
              <div class="text-sm text-purple-600">Verified</div>
              <div class="text-2xl font-bold text-purple-900">{{ usersStore.userStats.verified }}</div>
            </div>
          </div>
          
          <div class="flex gap-2">
            <Button @click="testUsersStore" :loading="usersStore.isLoading">
              Fetch Users
            </Button>
            <Button @click="usersStore.fetchUserStats" variant="outline">
              Refresh Stats
            </Button>
          </div>
          
          <div v-if="usersStore.error" class="text-red-600 text-sm">
            Error: {{ usersStore.error }}
          </div>
        </div>
      </Card>

      <!-- Auctions Store Test -->
      <Card>
        <template #header>
          <h3 class="text-lg font-semibold">Auctions Store Test</h3>
        </template>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-sm text-blue-600">Total Auctions</div>
              <div class="text-2xl font-bold text-blue-900">{{ auctionsStore.auctionStats.total }}</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-sm text-green-600">Active</div>
              <div class="text-2xl font-bold text-green-900">{{ auctionsStore.auctionStats.active }}</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-sm text-yellow-600">Completed</div>
              <div class="text-2xl font-bold text-yellow-900">{{ auctionsStore.auctionStats.completed }}</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
              <div class="text-sm text-purple-600">Total Revenue</div>
              <div class="text-2xl font-bold text-purple-900">{{ auctionsStore.totalRevenueCurrency }}</div>
            </div>
          </div>
          
          <div class="flex gap-2">
            <Button @click="testAuctionsStore" :loading="auctionsStore.isLoading">
              Fetch Auctions
            </Button>
            <Button @click="auctionsStore.fetchAuctionStats" variant="outline">
              Refresh Stats
            </Button>
            <Button 
              @click="toggleLiveMonitoring" 
              :variant="auctionsStore.liveMonitoringEnabled ? 'destructive' : 'outline'"
            >
              {{ auctionsStore.liveMonitoringEnabled ? 'Stop' : 'Start' }} Live Monitoring
            </Button>
          </div>
          
          <div v-if="auctionsStore.error" class="text-red-600 text-sm">
            Error: {{ auctionsStore.error }}
          </div>
        </div>
      </Card>

      <!-- Settings Store Test -->
      <Card>
        <template #header>
          <h3 class="text-lg font-semibold">Settings Store Test</h3>
        </template>
        
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-sm text-blue-600">Site Name</div>
              <div class="font-semibold text-blue-900">{{ settingsStore.generalSettings.site_name || 'Not Set' }}</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-sm text-green-600">Currency</div>
              <div class="font-semibold text-green-900">{{ settingsStore.generalSettings.currency }}</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-sm text-yellow-600">Maintenance Mode</div>
              <div class="font-semibold text-yellow-900">
                {{ settingsStore.isMaintenanceMode ? 'Enabled' : 'Disabled' }}
              </div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
              <div class="text-sm text-purple-600">Commission Rate</div>
              <div class="font-semibold text-purple-900">{{ settingsStore.auctionSettings.commission_rate }}%</div>
            </div>
          </div>
          
          <div class="flex gap-2">
            <Button @click="testSettingsStore" :loading="settingsStore.isLoading">
              Fetch All Settings
            </Button>
            <Button @click="settingsStore.clearCache" variant="outline">
              Clear Cache
            </Button>
          </div>
          
          <div v-if="settingsStore.error" class="text-red-600 text-sm">
            Error: {{ settingsStore.error }}
          </div>
        </div>
      </Card>

      <!-- Admin Store Test -->
      <Card>
        <template #header>
          <h3 class="text-lg font-semibold">Admin Store Test</h3>
        </template>
        
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-sm text-blue-600">Sidebar Collapsed</div>
              <div class="font-semibold text-blue-900">{{ adminStore.sidebarCollapsed ? 'Yes' : 'No' }}</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-sm text-green-600">Theme</div>
              <div class="font-semibold text-green-900">{{ adminStore.preferences.theme }}</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-sm text-yellow-600">Density</div>
              <div class="font-semibold text-yellow-900">{{ adminStore.preferences.density }}</div>
            </div>
          </div>
          
          <div class="flex gap-2">
            <Button @click="adminStore.toggleSidebar" variant="outline">
              Toggle Sidebar
            </Button>
            <Button @click="adminStore.toggleMobileMenu" variant="outline">
              Toggle Mobile Menu
            </Button>
            <Button @click="testAdminPreferences" variant="outline">
              Update Preferences
            </Button>
          </div>
        </div>
      </Card>
    </div>
  </AdminPageTemplate>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { AdminPageTemplate } from '@/components/admin';
import {
  useAdminStore,
  useAdminDashboard,
  useAdminUsers,
  useAdminAuctions,
  useAdminSettings
} from '@/stores/admin';
import { Button, Card } from '@/components/ui';

// Stores
const adminStore = useAdminStore();
const dashboardStore = useAdminDashboard();
const usersStore = useAdminUsers();
const auctionsStore = useAdminAuctions();
const settingsStore = useAdminSettings();

// Test methods
const testDashboardStore = async () => {
  try {
    await dashboardStore.fetchDashboardData();
    console.log('Dashboard store test successful');
  } catch (error) {
    console.error('Dashboard store test failed:', error);
  }
};

const testUsersStore = async () => {
  try {
    await usersStore.fetchUsers();
    console.log('Users store test successful');
  } catch (error) {
    console.error('Users store test failed:', error);
  }
};

const testAuctionsStore = async () => {
  try {
    await auctionsStore.fetchAuctions();
    console.log('Auctions store test successful');
  } catch (error) {
    console.error('Auctions store test failed:', error);
  }
};

const testSettingsStore = async () => {
  try {
    await settingsStore.fetchAllSettings();
    console.log('Settings store test successful');
  } catch (error) {
    console.error('Settings store test failed:', error);
  }
};

const testAdminPreferences = () => {
  adminStore.updatePreferences({
    theme: adminStore.preferences.theme === 'light' ? 'dark' : 'light',
    density: adminStore.preferences.density === 'comfortable' ? 'compact' : 'comfortable'
  });
  console.log('Admin preferences updated');
};

const toggleLiveMonitoring = () => {
  if (auctionsStore.liveMonitoringEnabled) {
    auctionsStore.stopLiveMonitoring();
  } else {
    auctionsStore.startLiveMonitoring();
  }
};

// Initialize stores on mount
onMounted(async () => {
  console.log('Initializing admin stores...');
  
  // Initialize admin store
  if (!adminStore.isInitialized) {
    adminStore.initialize();
  }
  
  // Test all stores (these will fail gracefully if APIs don't exist yet)
  try {
    await Promise.allSettled([
      dashboardStore.initialize(),
      usersStore.initialize(),
      auctionsStore.initialize(),
      settingsStore.initialize()
    ]);
    console.log('Store initialization completed');
  } catch (error) {
    console.log('Some stores failed to initialize (expected if APIs not implemented):', error);
  }
});
</script>
