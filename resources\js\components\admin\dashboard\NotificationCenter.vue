<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Notifications
        </h3>
        <div class="flex items-center space-x-2">
          <!-- Notification count badge -->
          <span
            v-if="totalNotifications > 0"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
          >
            {{ totalNotifications }}
          </span>
          
          <!-- Refresh button -->
          <button
            @click="refreshNotifications"
            :disabled="loading"
            class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Refresh notifications"
          >
            <svg
              class="h-4 w-4"
              :class="{ 'animate-spin': loading }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Loading state -->
      <div v-if="loading && notifications.length === 0" class="text-center py-8">
        <svg class="animate-spin h-8 w-8 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-gray-500 dark:text-gray-400">Loading notifications...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="text-center py-8">
        <svg class="h-8 w-8 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-red-600 dark:text-red-400 mb-2">Failed to load notifications</p>
        <button
          @click="refreshNotifications"
          class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Try again
        </button>
      </div>

      <!-- Empty state -->
      <div v-else-if="notifications.length === 0" class="text-center py-8">
        <svg class="h-8 w-8 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z" />
        </svg>
        <p class="text-gray-500 dark:text-gray-400">No notifications</p>
      </div>

      <!-- Notifications list -->
      <div v-else class="space-y-4">
        <!-- Expiring items section -->
        <div v-if="expiringItems.length > 0">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <svg class="h-4 w-4 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Expiring Soon ({{ expiringItems.length }})
          </h4>
          
          <div class="space-y-2">
            <div
              v-for="item in expiringItems.slice(0, 5)"
              :key="item.id"
              class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800"
            >
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {{ item.title }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  Expires {{ formatExpiryTime(item.expires_at) }}
                </p>
                <p class="text-xs text-gray-600 dark:text-gray-300">
                  Current bid: ${{ item.current_bid.toLocaleString() }} ({{ item.bid_count }} bids)
                </p>
              </div>
              
              <div class="ml-4 flex-shrink-0">
                <router-link
                  :to="`/admin-spa/items/${item.id}`"
                  class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                >
                  View
                </router-link>
              </div>
            </div>
            
            <!-- Show more link -->
            <div v-if="expiringItems.length > 5" class="text-center">
              <button
                @click="showAllExpiring = !showAllExpiring"
                class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                {{ showAllExpiring ? 'Show less' : `Show ${expiringItems.length - 5} more` }}
              </button>
            </div>
          </div>
        </div>

        <!-- System notifications -->
        <div v-if="systemNotifications.length > 0">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <svg class="h-4 w-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            System Updates
          </h4>
          
          <div class="space-y-2">
            <div
              v-for="notification in systemNotifications"
              :key="notification.id"
              class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
            >
              <p class="text-sm text-gray-900 dark:text-gray-100">
                {{ notification.message }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {{ formatTime(notification.created_at) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { api } from '@/utils/api';

interface ExpiringItem {
  id: number;
  title: string;
  expires_at: string;
  current_bid: number;
  bid_count: number;
}

interface SystemNotification {
  id: string;
  message: string;
  type: 'info' | 'warning' | 'error';
  created_at: string;
}

interface Props {
  branchId?: number | string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: true,
  refreshInterval: 60000 // 1 minute
});

// State
const loading = ref(false);
const error = ref<string | null>(null);
const expiringItems = ref<ExpiringItem[]>([]);
const systemNotifications = ref<SystemNotification[]>([]);
const showAllExpiring = ref(false);
let refreshTimer: number | null = null;

// Computed
const notifications = computed(() => [
  ...expiringItems.value,
  ...systemNotifications.value
]);

const totalNotifications = computed(() => 
  expiringItems.value.length + systemNotifications.value.length
);

// Methods
const fetchExpiringItems = async () => {
  try {
    const params: any = { days: 7 };
    if (props.branchId) {
      params.branch_id = props.branchId;
    }

    const response = await api.get<{ count: number; items: ExpiringItem[] }>('/admin/notifications/expiring-items', params);
    expiringItems.value = response.items || [];
  } catch (err) {
    console.error('Failed to fetch expiring items:', err);
  }
};

const fetchSystemNotifications = async () => {
  try {
    // Mock system notifications for now
    systemNotifications.value = [
      {
        id: 'sys_1',
        message: 'System backup completed successfully',
        type: 'info',
        created_at: new Date().toISOString()
      }
    ];
  } catch (err) {
    console.error('Failed to fetch system notifications:', err);
  }
};

const refreshNotifications = async () => {
  if (loading.value) return;
  
  loading.value = true;
  error.value = null;

  try {
    await Promise.all([
      fetchExpiringItems(),
      fetchSystemNotifications()
    ]);
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load notifications';
  } finally {
    loading.value = false;
  }
};

const formatExpiryTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `in ${diffDays} day${diffDays > 1 ? 's' : ''}`;
  } else if (diffHours > 0) {
    return `in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  } else {
    return 'soon';
  }
};

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

const startAutoRefresh = () => {
  if (props.autoRefresh && !refreshTimer) {
    refreshTimer = window.setInterval(refreshNotifications, props.refreshInterval);
  }
};

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// Lifecycle
onMounted(() => {
  refreshNotifications();
  startAutoRefresh();
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>
