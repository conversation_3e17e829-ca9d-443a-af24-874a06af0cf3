<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
      <!-- Date Range Filter -->
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Date Range
        </label>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
          <!-- Quick presets -->
          <div class="flex space-x-1">
            <button
              v-for="preset in datePresets"
              :key="preset.key"
              @click="selectDatePreset(preset)"
              :class="[
                'px-3 py-2 text-xs font-medium rounded-md transition-colors',
                selectedPreset === preset.key
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              ]"
            >
              {{ preset.label }}
            </button>
          </div>
          
          <!-- Custom date inputs -->
          <div class="flex space-x-2">
            <input
              v-model="dateFrom"
              type="date"
              class="block w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              @change="handleDateChange"
            />
            <span class="flex items-center text-gray-500 dark:text-gray-400">to</span>
            <input
              v-model="dateTo"
              type="date"
              class="block w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              @change="handleDateChange"
            />
          </div>
        </div>
      </div>

      <!-- Branch Selector -->
      <div class="flex-shrink-0 w-full lg:w-64">
        <BranchSelector
          v-model="selectedBranch"
          :loading="loading"
          @change="handleBranchChange"
        />
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-3">
        <!-- Refresh Button -->
        <button
          @click="handleRefresh"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <svg
            class="w-4 h-4 mr-2"
            :class="{ 'animate-spin': loading }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>

        <!-- Export Button -->
        <button
          @click="handleExport"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12" />
          </svg>
          Export
        </button>
      </div>
    </div>

    <!-- Active Filters Display -->
    <div v-if="hasActiveFilters" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2">
        <span class="text-sm text-gray-500 dark:text-gray-400">Active filters:</span>
        
        <!-- Date range chip -->
        <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {{ formatDateRange }}
          <button
            @click="clearDateFilter"
            class="ml-2 inline-flex items-center justify-center w-4 h-4 text-blue-400 hover:text-blue-600 dark:text-blue-300 dark:hover:text-blue-100"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Branch chip -->
        <div
          v-if="selectedBranch"
          class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
        >
          {{ selectedBranchName }}
          <button
            @click="clearBranchFilter"
            class="ml-2 inline-flex items-center justify-center w-4 h-4 text-green-400 hover:text-green-600 dark:text-green-300 dark:hover:text-green-100"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Clear all button -->
        <button
          @click="clearAllFilters"
          class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 underline"
        >
          Clear all
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import BranchSelector from './BranchSelector.vue';

interface DatePreset {
  key: string;
  label: string;
  days: number;
}

interface Branch {
  id: number;
  name: string;
}

interface Props {
  loading?: boolean;
}

interface Emits {
  (e: 'filter-change', filters: {
    date_from: string;
    date_to: string;
    branch_id?: number | string;
  }): void;
  (e: 'refresh'): void;
  (e: 'export'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// State
const dateFrom = ref('');
const dateTo = ref('');
const selectedBranch = ref<number | string>('');
const selectedPreset = ref('30d');
const branches = ref<Branch[]>([]);

// Date presets
const datePresets: DatePreset[] = [
  { key: '7d', label: '7 days', days: 7 },
  { key: '30d', label: '30 days', days: 30 },
  { key: '90d', label: '90 days', days: 90 },
  { key: '1y', label: '1 year', days: 365 }
];

// Computed
const hasActiveFilters = computed(() => {
  return dateFrom.value || dateTo.value || selectedBranch.value;
});

const formatDateRange = computed(() => {
  if (dateFrom.value && dateTo.value) {
    return `${formatDate(dateFrom.value)} - ${formatDate(dateTo.value)}`;
  } else if (dateFrom.value) {
    return `From ${formatDate(dateFrom.value)}`;
  } else if (dateTo.value) {
    return `Until ${formatDate(dateTo.value)}`;
  }
  return '';
});

const selectedBranchName = computed(() => {
  if (!selectedBranch.value) return '';
  const branch = branches.value.find(b => b.id === Number(selectedBranch.value));
  return branch?.name || '';
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const selectDatePreset = (preset: DatePreset) => {
  selectedPreset.value = preset.key;
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - preset.days);
  
  dateFrom.value = startDate.toISOString().split('T')[0];
  dateTo.value = endDate.toISOString().split('T')[0];
  
  emitFilterChange();
};

const handleDateChange = () => {
  selectedPreset.value = ''; // Clear preset selection when custom dates are used
  emitFilterChange();
};

const handleBranchChange = (branch: Branch | null) => {
  emitFilterChange();
};

const handleRefresh = () => {
  emit('refresh');
};

const handleExport = () => {
  emit('export');
};

const clearDateFilter = () => {
  dateFrom.value = '';
  dateTo.value = '';
  selectedPreset.value = '';
  emitFilterChange();
};

const clearBranchFilter = () => {
  selectedBranch.value = '';
  emitFilterChange();
};

const clearAllFilters = () => {
  dateFrom.value = '';
  dateTo.value = '';
  selectedBranch.value = '';
  selectedPreset.value = '';
  emitFilterChange();
};

const emitFilterChange = () => {
  const filters: any = {
    date_from: dateFrom.value,
    date_to: dateTo.value
  };
  
  if (selectedBranch.value) {
    filters.branch_id = selectedBranch.value;
  }
  
  emit('filter-change', filters);
};

// Initialize with default 30-day range
onMounted(() => {
  selectDatePreset(datePresets.find(p => p.key === '30d')!);
  
  // Load branches for name display
  if ((window as any).branches) {
    branches.value = (window as any).branches;
  }
});
</script>
